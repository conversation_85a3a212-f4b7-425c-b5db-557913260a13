name: base
channels:
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/rapidsai/
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_gnu
  - archspec=0.2.2=pyhd8ed1ab_0
  - boltons=23.0.0=py310h06a4308_0
  - brotli-python=1.1.0=py310hc6cd4ac_1
  - brotlipy=0.7.0=py310h5764c6d_1005
  - bzip2=1.0.8=h7f98852_4
  - c-ares=1.24.0=hd590300_0
  - ca-certificates=2025.1.31=hbcca054_0
  - certifi=2024.12.14=pyhd8ed1ab_0
  - cffi=1.15.1=py310h5eee18b_3
  - charset-normalizer=3.1.0=pyhd8ed1ab_0
  - colorama=0.4.6=pyhd8ed1ab_0
  - conda=23.11.0=py310hff52083_1
  - conda-content-trust=0.1.3=py310h06a4308_0
  - conda-libmamba-solver=23.12.0=pyhd8ed1ab_0
  - conda-package-handling=2.2.0=pyh38be061_0
  - conda-package-streaming=0.9.0=pyhd8ed1ab_0
  - cryptography=41.0.1=py310h75e40e8_0
  - distro=1.9.0=pyhd8ed1ab_0
  - fmt=10.1.1=h00ab1b0_1
  - icu=73.2=h59595ed_0
  - idna=3.4=py310h06a4308_0
  - jplephem=2.21=pyh864a33b_0
  - jsonpatch=1.32=pyhd3eb1b0_0
  - jsonpointer=2.1=pyhd3eb1b0_0
  - keyutils=1.6.1=h166bdaf_0
  - krb5=1.21.2=h659d440_0
  - ld_impl_linux-64=2.40=h41732ed_0
  - libarchive=3.7.2=h2aa1ff5_1
  - libblas=3.9.0=20_linux64_openblas
  - libcblas=3.9.0=20_linux64_openblas
  - libcurl=8.5.0=hca28451_0
  - libedit=3.1.20191231=he28a2e2_2
  - libev=4.33=h516909a_1
  - libffi=3.4.2=h6a678d5_6
  - libgcc-ng=13.1.0=he5830b7_0
  - libgfortran-ng=13.2.0=h69a702a_0
  - libgfortran5=13.2.0=ha4646dd_0
  - libgomp=13.1.0=he5830b7_0
  - libiconv=1.17=h166bdaf_0
  - liblapack=3.9.0=20_linux64_openblas
  - libmamba=1.5.6=had39da4_0
  - libmambapy=1.5.6=py310h39ff949_0
  - libnghttp2=1.58.0=h47da74e_1
  - libnsl=2.0.0=h7f98852_0
  - libopenblas=0.3.25=pthreads_h413a1c8_0
  - libsolv=0.7.24=hfc55251_1
  - libsqlite=3.42.0=h2797004_0
  - libssh2=1.11.0=h0841786_0
  - libstdcxx-ng=13.1.0=hfd8a6a1_0
  - libuuid=2.38.1=h0b41bf4_0
  - libxml2=2.12.3=h232c23b_0
  - libzlib=1.2.13=hd590300_5
  - llvm-openmp=16.0.6=h4dfa4b3_0
  - lz4-c=1.9.4=hcb278e6_0
  - lzo=2.10=h516909a_1000
  - mamba=1.5.6=py310h51d5547_0
  - menuinst=2.0.1=py310hff52083_0
  - ncurses=6.4=h6a678d5_0
  - numpy=1.26.3=py310hb13e2d6_0
  - openssl=3.3.1=hb9d3cd8_3
  - packaging=23.1=pyhd8ed1ab_0
  - pip=23.1.2=pyhd8ed1ab_0
  - platformdirs=4.1.0=pyhd8ed1ab_0
  - pluggy=1.2.0=pyhd8ed1ab_0
  - pybind11-abi=4=hd8ed1ab_3
  - pycosat=0.6.4=py310h5764c6d_1
  - pycparser=2.21=pyhd3eb1b0_0
  - pyopenssl=23.2.0=pyhd8ed1ab_1
  - pysocks=1.7.1=pyha2e5f31_6
  - python=3.10.12=hd12c33a_0_cpython
  - python_abi=3.10=3_cp310
  - pytz=2023.3.post1=pyhd8ed1ab_0
  - readline=8.2=h8228510_1
  - reproc=14.2.4=h0b41bf4_0
  - reproc-cpp=14.2.4=hcb278e6_0
  - requests=2.31.0=pyhd8ed1ab_0
  - ruamel.yaml=0.17.32=py310h2372a71_0
  - ruamel.yaml.clib=0.2.7=py310h1fa729e_1
  - setuptools=68.0.0=pyhd8ed1ab_0
  - sgp4=2.10=pyh6957fcd_0
  - six=1.16.0=pyhd3eb1b0_1
  - skyfield=1.45=pyh1a96a4e_0
  - sqlite=3.42.0=h2c6b66d_0
  - tk=8.6.12=h1ccaba5_0
  - toolz=0.12.0=py310h06a4308_0
  - tqdm=4.65.0=pyhd8ed1ab_1
  - truststore=0.8.0=pyhd8ed1ab_0
  - tzdata=2023c=h04d1e81_0
  - urllib3=2.0.3=pyhd8ed1ab_1
  - wheel=0.40.0=pyhd8ed1ab_0
  - xz=5.4.2=h5eee18b_0
  - yaml-cpp=0.8.0=h59595ed_0
  - zlib=1.2.13=hd590300_5
  - zstandard=0.22.0=py310h1275a96_0
  - zstd=1.5.5=hfc55251_0
  - pip:
      - absl-py==2.0.0
      - accelerate==1.7.0
      - aiohttp==3.9.1
      - aiosignal==1.3.1
      - aiosqlite==0.19.0
      - annotated-types==0.6.0
      - anyio==4.2.0
      - apexpy==2.0.1
      - argon2-cffi==23.1.0
      - argon2-cffi-bindings==21.2.0
      - arrow==1.3.0
      - asttokens==2.4.1
      - async-lru==2.0.4
      - attrs==23.2.0
      - babel==2.14.0
      - beautifulsoup4==4.12.2
      - bleach==6.1.0
      - blinker==1.7.0
      - bokeh==3.1.1
      - brotli==1.1.0
      - cached-property==1.5.2
      - cachetools==5.3.2
      - certifi==2024.8.30
      - cffi==1.16.0
      - cftime==1.6.3
      - charset-normalizer==3.3.2
      - click==8.1.7
      - cloudpickle==3.0.0
      - colorama==0.4.6
      - comm==0.2.1
      - contourpy==1.2.0
      - cryptography==41.0.7
      - cx-oracle==8.3.0
      - cycler==0.12.1
      - dask==2023.12.1
      - dataclasses-json==0.6.3
      - debugpy==1.8.0
      - decorator==5.1.1
      - deepmerge==1.1.1
      - defusedxml==0.7.1
      - distributed==2023.12.1
      - einops==0.8.1
      - entrypoints==0.4
      - exceptiongroup==1.2.0
      - executing==2.0.1
      - faiss-cpu==1.7.4
      - fastjsonschema==2.19.1
      - filelock==3.13.1
      - fonttools==4.51.0
      - fqdn==1.5.1
      - frozenlist==1.4.1
      - fsspec==2023.12.2
      - gitdb==4.0.12
      - gitpython==3.1.44
      - gmpy2==2.1.2
      - google-auth==2.26.1
      - google-auth-oauthlib==1.2.0
      - gpytorch==1.11
      - graphviz==0.20.3
      - greenlet==3.0.3
      - grpcio==1.60.0
      - h3==3.7.6
      - h5py==3.10.0
      - hf-xet==1.1.3
      - huggingface-hub==0.33.0
      - idna==3.6
      - importlib-metadata==7.0.1
      - importlib-resources==6.1.1
      - ipykernel==6.28.0
      - ipython==8.19.0
      - ipywidgets==8.1.2
      - isoduration==20.11.0
      - jaxtyping==0.2.25
      - jedi==0.19.1
      - jinja2==3.1.2
      - joblib==1.3.2
      - jplephem==2.21
      - json5==0.9.14
      - jsonpatch==1.33
      - jsonpath-ng==1.6.0
      - jsonpointer==2.4
      - jsonschema==4.20.0
      - jsonschema-specifications==2023.12.1
      - jupyter-ai==2.8.1
      - jupyter-ai-chatgpt==0.5.0
      - jupyter-ai-magics==2.8.1
      - jupyter-client==7.4.9
      - jupyter-core==5.7.0
      - jupyter-events==0.9.0
      - jupyter-lsp==2.2.1
      - jupyter-server==2.12.2
      - jupyter-server-terminals==0.5.1
      - jupyterlab==4.0.10
      - jupyterlab-pygments==0.3.0
      - jupyterlab-server==2.25.2
      - jupyterlab-widgets==3.0.10
      - kiwisolver==1.4.5
      - langchain==0.0.350
      - langchain-community==0.0.7
      - langchain-core==0.1.3
      - langsmith==0.0.77
      - linear-operator==0.5.2
      - llvmlite==0.41.1
      - locket==1.0.0
      - markdown==3.5.1
      - markupsafe==2.1.3
      - marshmallow==3.20.1
      - matplotlib==3.8.4
      - matplotlib-inline==0.1.6
      - mistune==3.0.2
      - mkl-fft==1.3.1
      - mkl-random==1.2.2
      - mkl-service==2.4.0
      - mpmath==1.3.0
      - msgpack==1.0.7
      - multidict==6.0.4
      - munkres==1.1.4
      - mypy-extensions==1.0.0
      - narwhals==1.42.1
      - nbclient==0.8.0
      - nbconvert==7.14.0
      - nbformat==5.9.2
      - nest-asyncio==1.5.8
      - netcdf4==1.6.5
      - networkx==3.2.1
      - notebook==7.0.6
      - notebook-shim==0.2.3
      - numba==0.58.1
      - numpy==1.24.3
      - oauthlib==3.2.2
      - openai==0.28.1
      - oracledb==2.1.2
      - overrides==7.4.0
      - packaging==23.2
      - pandas==2.1.4
      - pandocfilters==1.5.0
      - parso==0.8.3
      - partd==1.4.1
      - patsy==0.5.6
      - pexpect==4.8.0
      - pickleshare==0.7.5
      - pillow==10.1.0
      - pip==23.3.2
      - pkgutil-resolve-name==1.3.10
      - platformdirs==4.1.0
      - plotly==6.1.2
      - ply==3.11
      - polars==0.20.2
      - pooch==1.8.0
      - prometheus-client==0.19.0
      - prompt-toolkit==3.0.42
      - protobuf==4.24.4
      - psutil==5.9.7
      - ptyprocess==0.7.0
      - pure-eval==0.2.2
      - pyasn1==0.5.1
      - pyasn1-modules==0.3.0
      - pycparser==2.21
      - pydantic==2.5.3
      - pydantic-core==2.14.6
      - pygments==2.17.2
      - pygrinder==0.4
      - pyjwt==2.8.0
      - pyopenssl==23.3.0
      - pyparsing==3.1.1
      - pypots==0.3
      - pyqt5==5.15.9
      - pyqt5-sip==12.12.2
      - pysocks==1.7.1
      - python-dateutil==2.8.2
      - python-json-logger==2.0.7
      - pytz==2023.3.post1
      - pyu2f==0.1.5
      - pyyaml==6.0.1
      - pyzmq==24.0.1
      - referencing==0.32.0
      - regex==2023.12.25
      - requests==2.31.0
      - requests-oauthlib==1.3.1
      - rfc3339-validator==0.1.4
      - rfc3986-validator==0.1.1
      - rpds-py==0.16.2
      - rsa==4.9
      - safetensors==0.5.3
      - scikit-learn==1.3.2
      - scipy==1.10.1
      - seaborn==0.13.2
      - send2trash==1.8.2
      - sentry-sdk==2.30.0
      - setproctitle==1.3.6
      - setuptools==68.2.2
      - sgp4==2.22
      - sip==6.7.12
      - six==1.16.0
      - skyfield==1.45
      - smmap==5.0.2
      - snakeviz==2.2.0
      - sniffio==1.3.0
      - sortedcontainers==2.4.0
      - soupsieve==2.5
      - sqlalchemy==2.0.25
      - stack-data==0.6.2
      - statsmodels==0.14.4
      - sympy==1.12
      - tblib==3.0.0
      - tenacity==8.2.3
      - tensorboard==2.15.1
      - tensorboard-data-server==0.7.0
      - terminado==0.18.0
      - threadpoolctl==3.2.0
      - tiktoken==0.5.2
      - timezonefinder==6.2.0
      - tinycss2==1.2.1
      - toml==0.10.2
      - tomli==2.0.1
      - toolz==0.12.0
      - torch==2.1.1
      - torch-geometric==2.4.0
      - torch-scatter==2.1.2
      - torch-sparse==0.6.18
      - torchaudio==2.1.1
      - torchvision==0.16.1
      - tornado==6.3.3
      - tqdm==4.66.1
      - traitlets==5.14.1
      - triton==2.1.0
      - tsdb==0.3
      - typeguard==2.13.3
      - types-python-dateutil==*********
      - typing-extensions==4.9.0
      - typing-inspect==0.9.0
      - typing-utils==0.1.0
      - tzdata==2023.4
      - uri-template==1.3.0
      - urllib3==2.1.0
      - wandb==0.20.1
      - wcwidth==0.2.12
      - webcolors==1.13
      - webencodings==0.5.1
      - websocket-client==1.7.0
      - werkzeug==3.0.1
      - wheel==0.42.0
      - widgetsnbextension==4.0.10
      - xyzservices==2023.10.1
      - yarl==1.9.3
      - zict==3.0.0
      - zipp==3.17.0
prefix: /home/<USER>/miniconda3
