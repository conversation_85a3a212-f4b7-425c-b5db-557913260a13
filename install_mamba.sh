#!/bin/bash
# 安装mamba - conda的高性能替代品

echo "=== 安装mamba ==="
echo "mamba是conda的高性能替代品，具有以下优势："
echo "- 更快的依赖解析速度"
echo "- 更快的包下载和安装"
echo "- 完全兼容conda命令"
echo ""

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "错误: 未找到conda命令，请先安装Miniconda或Anaconda"
    exit 1
fi

# 检查mamba是否已安装
if command -v mamba &> /dev/null; then
    echo "mamba已经安装！"
    mamba --version
    echo ""
    echo "使用方法："
    echo "- 创建环境: mamba create -n myenv python=3.10"
    echo "- 安装包: mamba install numpy pandas"
    echo "- 搜索包: mamba search pytorch"
    exit 0
fi

echo "正在安装mamba到base环境..."
conda install -n base -c conda-forge mamba -y

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ mamba安装成功！"
    echo ""
    echo "验证安装:"
    mamba --version
    echo ""
    echo "🚀 使用方法："
    echo "- mamba可以完全替代conda命令"
    echo "- 创建环境: mamba create -n myenv python=3.10"
    echo "- 安装包: mamba install numpy pandas matplotlib"
    echo "- 搜索包: mamba search pytorch"
    echo "- 更新包: mamba update numpy"
    echo ""
    echo "💡 提示: mamba命令与conda完全兼容，但速度更快！"
else
    echo "❌ mamba安装失败，请检查网络连接或重试"
    exit 1
fi
