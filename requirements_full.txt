# 完整的pip包列表 (从pip list --format=freeze导出)
# 核心机器学习库
torch==2.1.1
torch-geometric==2.4.0
torch-scatter==2.1.2
torch-sparse==0.6.18
torchaudio==2.1.1
torchvision==0.16.1
triton==2.1.0

# Transformers和PEFT
transformers==4.30.0  # 注意：实际安装的可能是更新版本
peft==0.4.0  # 注意：实际安装的可能是更新版本
accelerate==1.7.0
safetensors==0.5.3
huggingface-hub==0.33.0

# 科学计算
numpy==1.24.3
scipy==1.10.1
scikit-learn==1.3.2
pandas==2.1.4
h5py==3.10.0
joblib==1.3.2

# 数据处理和工具
einops==0.8.1
tqdm==4.66.1

# 可视化
matplotlib==3.8.4
seaborn==0.13.2
plotly==6.1.2

# Jupyter相关
jupyter-core==5.7.0
jupyterlab==4.0.10
ipython==8.19.0
ipykernel==6.28.0
notebook==7.0.6

# 其他重要依赖
wandb==0.20.1
tensorboard==2.15.1
pyyaml==6.0.1
requests==2.31.0
