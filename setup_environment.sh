#!/bin/bash
# TEC-MoLLM 环境配置脚本
# 用于重建与当前环境兼容的Miniconda环境

echo "=== TEC-MoLLM 环境配置脚本 ==="
echo "当前环境信息："
echo "- Python: 3.10.12"
echo "- PyTorch: 2.1.1"
echo "- CUDA: 12.1"
echo "- Ubuntu: 22.04.4 LTS"
echo ""

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "错误: 未找到conda命令，请先安装Miniconda或Anaconda"
    exit 1
fi

# 创建新环境
ENV_NAME="tec-mollm"
echo "创建新的conda环境: $ENV_NAME"
conda create -n $ENV_NAME python=3.10 -y

# 激活环境
echo "激活环境..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate $ENV_NAME

# 添加清华镜像源
echo "配置conda镜像源..."
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/rapidsai/
conda config --add channels conda-forge
conda config --add channels defaults

# 安装基础科学计算包
echo "安装基础科学计算包..."
conda install -y numpy=1.24.3 scipy=1.10.1 matplotlib=3.8.4

# 安装PyTorch (CUDA 12.1版本)
echo "安装PyTorch..."
pip install torch==2.1.1 torchvision==0.16.1 torchaudio==2.1.1 --index-url https://download.pytorch.org/whl/cu121

# 安装PyTorch Geometric
echo "安装PyTorch Geometric..."
pip install torch-geometric==2.4.0
pip install torch-scatter==2.1.2 torch-sparse==0.6.18 -f https://data.pyg.org/whl/torch-2.1.0+cu121.html

# 安装Transformers和PEFT
echo "安装Transformers和相关库..."
pip install transformers>=4.30.0 peft>=0.4.0 accelerate

# 安装其他核心依赖
echo "安装其他核心依赖..."
pip install -r requirements.txt

echo ""
echo "=== 环境配置完成 ==="
echo "激活环境: conda activate $ENV_NAME"
echo "验证安装: python -c \"import torch; print(f'PyTorch: {torch.__version__}, CUDA: {torch.version.cuda}')\""
echo ""
