{"master": {"tasks": [{"id": 1, "title": "Data Loading and Chronological Splitting", "description": "Load TEC, space weather indices, and coordinates from the two specified HDF5 files. This task involves reading the datasets and splitting them chronologically into training (2014), validation (Jan-Jun 2015), and test (Jul-Dec 2015) sets.", "details": "Use the `h5py` library to read the HDF5 files. Create a data loading script that accesses `/ionosphere/TEC`, `/space_weather_indices`, and `/coordinates`. The script should handle the file paths `/home/<USER>/TEC-MoLLM/data/raw/CRIM_SW2hr_AI_v1.2_2014_DataDrivenRange_CN.hdf5` and `..._2015_...`. Implement a function to partition the data based on the specified date ranges. Ensure the conda environment `tecgpt_env` is used.", "testStrategy": "Verify the shapes of the loaded arrays for TEC, indices, and coordinates. Check that the number of time steps for train, validation, and test sets match the PRD specifications (4380, 2172, 2208). Assert that the data is loaded in correct chronological order.", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Setup Environment and Install Dependencies", "description": "Verify the 'tecgpt_env' conda environment is active and install the 'h5py' library. This is a foundational step to ensure the script can execute and interact with HDF5 files.", "dependencies": [], "details": "Activate the conda environment using `conda activate tecgpt_env`. Check if `h5py` is installed by running `pip list`. If not present, install it using `pip install h5py`. This step ensures all subsequent code will run in the correct environment with the necessary packages.", "status": "done", "testStrategy": "Execute `python -c \"import h5py; print(h5py.__version__)\"` within the activated `tecgpt_env`. A successful import and version printout will confirm the environment is correctly configured."}, {"id": 2, "title": "Implement HDF5 Data Reader Function", "description": "Create a Python function to read specified datasets from a single HDF5 file. This function will serve as a reusable utility to extract data from both the 2014 and 2015 files.", "dependencies": [1], "details": "Define a function, e.g., `load_data_from_hdf5(file_path)`. This function should take a file path string as input. Inside, use `h5py.File(file_path, 'r')` to open the file. Extract the '/ionosphere/TEC', '/space_weather_indices', and '/coordinates' datasets, along with any associated time information. The function should return these datasets, for example, in a dictionary.", "status": "done", "testStrategy": "Call the function with the path to the 2014 HDF5 file. Assert that the returned object is a dictionary containing non-empty NumPy arrays for the keys 'TEC', 'space_weather_indices', and 'coordinates'."}, {"id": 3, "title": "Aggregate Data from Both HDF5 Files", "description": "Write a script that utilizes the reader function from subtask 2 to load data from both the 2014 and 2015 HDF5 files and concatenates them into single, time-ordered arrays for each data type (TEC, indices, coordinates, and time).", "dependencies": [2], "details": "The script will call the `load_data_from_hdf5` function for both `..._2014_...hdf5` and `..._2015_...hdf5` files. After loading, concatenate the corresponding arrays (e.g., using `numpy.concatenate`). Ensure the timestamps are also concatenated and that the data arrays remain aligned with the correct timestamps.", "status": "done", "testStrategy": "Verify the shape of the final concatenated TEC array. Its first dimension should be the sum of the samples from the 2014 and 2015 files. Check that the final timestamp array is monotonically increasing."}, {"id": 4, "title": "Implement Chronological Splitting Function", "description": "Create a function that takes the aggregated data arrays and performs a chronological split based on specified date ranges: training (2014), validation (Jan-Jun 2015), and test (Jul-Dec 2015).", "dependencies": [3], "details": "The function signature should be `split_data(data, timestamps)`. It will need to identify the indices corresponding to the date boundaries. For example, find the index where the year changes from 2014 to 2015, and the index for the start of July 2015. Use these indices to slice the aggregated data arrays into three distinct sets.", "status": "done", "testStrategy": "After splitting, check the time range of each dataset. The training set's timestamps should all be from 2014. The validation set's timestamps should range from Jan 2015 to Jun 2015. The test set's timestamps should range from Jul 2015 to Dec 2015. The sum of samples in all three splits must equal the total number of samples in the aggregated data."}, {"id": 5, "title": "Integrate into a Master Data Loading Script", "description": "Combine all functionalities into a single, executable main script. This script will handle file paths, call the aggregation logic, perform the chronological split, and return the final training, validation, and test datasets.", "dependencies": [4], "details": "Create a main script (e.g., `load_data.py`). This script will define the HDF5 file paths, call the functions to load and aggregate data, and then call the splitting function. The script should return the three data splits (e.g., as a dictionary of dictionaries: `{'train': {...}, 'val': {...}, 'test': {...}}`) for use in other parts of the project.", "status": "done", "testStrategy": "Run the main script from the command line within the `tecgpt_env`. Verify that it executes without error and that the returned data structures for train, validation, and test sets are correctly structured, have the expected data shapes, and are not empty."}]}, {"id": 2, "title": "Graph Construction: Adjacency Matrix", "description": "Construct the spatial graph adjacency matrix based on geographical coordinates. This involves calculating distances between all grid nodes and creating a sparse, normalized adjacency matrix.", "details": "Use the `latitude` and `longitude` arrays (41x71 grid) to calculate the Haversine distance between all 2911 node pairs. A distance threshold (e.g., 150km) will be used to create the binary adjacency matrix `A`. Normalize `A` using symmetric normalization: `D^(-1/2) * A * D^(-1/2)`, where `D` is the degree matrix. Save the resulting `edge_index` and `edge_weight` in a format suitable for PyTorch Geometric (e.g., `.pt` file) to avoid re-computation.", "testStrategy": "Check the shape of the resulting adjacency matrix or edge index. Visualize a small subset of the graph to ensure connections are local. Verify that the matrix is symmetric and the saved file can be loaded correctly.", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Calculate Pairwise Haversine Distances", "description": "Using the provided 41x71 latitude and longitude grids, flatten the coordinates into a list of 2911 nodes and compute the pairwise Haversine distance matrix between all nodes.", "dependencies": [], "details": "The input consists of two 2D numpy arrays: `latitude` and `longitude`. These should be reshaped into a single array of (2911, 2) coordinate pairs. A function implementing the Haversine formula will then be used to compute a 2911x2911 matrix where each element `(i, j)` is the distance in kilometers between node `i` and node `j`.", "status": "done", "testStrategy": "Verify the shape of the resulting distance matrix is (2911, 2911). Check that the matrix is symmetric and has a zero diagonal. Manually calculate the distance between two known points and compare it with the matrix output."}, {"id": 2, "title": "Construct Binary Adjacency Matrix", "description": "Create a binary adjacency matrix `A` by applying a distance threshold to the pairwise distance matrix.", "dependencies": [1], "details": "Using the 2911x2911 distance matrix from the previous subtask, create a new matrix `A`. For each element `(i, j)`, if the distance is less than or equal to the threshold (150km) and `i` is not equal to `j`, set `A[i, j] = 1`. Otherwise, set `A[i, j] = 0`.", "status": "done", "testStrategy": "Confirm the output matrix `A` is of shape (2911, 2911), contains only binary values (0 or 1), and has a zero diagonal. Check a few entries to ensure the thresholding logic was applied correctly."}, {"id": 3, "title": "Compute Degree Matrix", "description": "Calculate the degree matrix `D` from the binary adjacency matrix `A`.", "dependencies": [2], "details": "The degree matrix `D` is a diagonal matrix where the diagonal element `D[i, i]` is the degree of node `i`. The degree is calculated by summing the values in the i-th row of the binary adjacency matrix `A`. All off-diagonal elements of `D` are 0.", "status": "done", "testStrategy": "Verify that the resulting matrix `D` is a diagonal matrix of shape (2911, 2911). Check that the diagonal values correctly represent the sum of their corresponding rows in matrix `A`."}, {"id": 4, "title": "Perform Symmetric Normalization", "description": "Normalize the binary adjacency matrix `A` using the symmetric normalization formula: `D^(-1/2) * A * D^(-1/2)`.", "dependencies": [2, 3], "details": "First, compute `D^(-1/2)` by taking the element-wise inverse square root of the diagonal of `D`. Handle potential division by zero for nodes with a degree of 0 by setting their corresponding inverse value to 0. Then, perform the matrix multiplications to obtain the final normalized adjacency matrix.", "status": "done", "testStrategy": "Check that the resulting normalized matrix is symmetric. Verify that all values are between 0 and 1. For an edge between nodes `i` and `j` with degrees `d_i` and `d_j`, the normalized weight should be `1 / sqrt(d_i * d_j)`."}, {"id": 5, "title": "Convert to PyG Format and Save", "description": "Convert the normalized, sparse adjacency matrix into `edge_index` and `edge_weight` tensors suitable for PyTorch Geometric and save them to a file.", "dependencies": [4], "details": "From the normalized adjacency matrix, extract the coordinates (indices) of non-zero elements to form the `edge_index` tensor of shape `[2, num_edges]`. Extract the corresponding non-zero values to form the `edge_weight` tensor of shape `[num_edges]`. Save both tensors to a single `.pt` file using `torch.save()`.", "status": "done", "testStrategy": "Load the saved `.pt` file. Verify that `edge_index` and `edge_weight` can be loaded correctly and have the expected shapes and data types (long for index, float for weight). Reconstruct a small portion of the sparse matrix from the loaded tensors to ensure data integrity."}]}, {"id": 3, "title": "Feature and Target Engineering", "description": "Engineer the input feature tensor `X` and the target tensor `Y`. This includes stacking TEC with broadcasted space weather indices for `X`, and creating the multi-step prediction target `Y`.", "details": "For input `X`: \n1. Take the `ionosphere/TEC` data `(N_times, 41, 71)`. \n2. Take the 5 space weather indices `(N_times,)`. Remember to apply the scale_factor for Kp_Index. \n3. Broadcast each index to `(N_times, 41, 71)`. \n4. Stack TEC and the 5 broadcasted indices along a new dimension to get `X` with shape `(N_times, 41, 71, 6)`. \nFor target `Y`: \nCreate a tensor where `Y[t]` contains the TEC values for the next 12 time steps, resulting in a shape of `(N_times, 41, 71, 12)`.", "testStrategy": "Validate the final shapes of `X` and `Y` tensors. Check a few slices to ensure the broadcasting and stacking operations were performed correctly. For `Y`, verify that `Y[t]` correctly corresponds to `TEC[t+1:t+13]`.", "priority": "high", "dependencies": [1], "status": "done", "subtasks": [{"id": 1, "title": "Load and Scale Space Weather Indices", "description": "Load the five specified space weather indices from the dataset and apply the necessary scaling factor to the Kp_Index.", "dependencies": [], "details": "Read the following 1D arrays (shape: N_times) from the data source: `Kp_Index`, `Dst_Index`, `AE_Index`, `Solar_Wind_Speed`, and `Proton_Density`. Apply the provided `scale_factor` to the `Kp_Index` array.", "status": "done", "testStrategy": "Verify that five 1D arrays are loaded. Confirm the shape of each array is (N_times,). Check a sample of Kp_Index values to ensure the scale_factor has been correctly applied."}, {"id": 2, "title": "Broadcast Space Weather Indices", "description": "Reshape and broadcast the five 1D space weather index arrays to match the spatial dimensions (41, 71) of the TEC data.", "dependencies": [1], "details": "For each of the five scaled index arrays (shape: N_times), expand its dimensions and broadcast it to a shape of (N_times, 41, 71). This prepares the indices for stacking with the TEC map.", "status": "done", "testStrategy": "Confirm that the output consists of five tensors. Verify that the shape of each output tensor is (N_times, 41, 71)."}, {"id": 3, "title": "Construct Feature Tensor X", "description": "Load the TEC data and stack it with the five broadcasted space weather indices to create the final feature tensor X.", "dependencies": [2], "details": "Load the `ionosphere/TEC` data (shape: N_times, 41, 71). Stack the TEC tensor and the five broadcasted index tensors from the previous subtask along a new channel dimension (axis=-1).", "status": "done", "testStrategy": "Verify the final shape of the tensor X is (N_times, 41, 71, 6). Check that the slice X[..., 0] matches the original TEC data and X[..., 1] through X[..., 5] match the broadcasted indices."}, {"id": 4, "title": "Construct Multi-Step Target Tensor Y", "description": "Using the TEC data, create the target tensor Y for multi-step prediction, where each sample contains the subsequent 12 TEC maps.", "dependencies": [], "details": "Load the `ionosphere/TEC` data (shape: N_times, 41, 71). Use a sliding window approach to create a new tensor Y. For each time step `t`, Y[t] should be a stack of the TEC maps from `t+1` to `t+12` along a new channel dimension.", "status": "done", "testStrategy": "Verify the shape of the resulting Y tensor is (N_times - 12, 41, 71, 12). Check that Y[t, ..., i] is identical to the original TEC[t + i + 1, ...]."}, {"id": 5, "title": "Align Feature and Target Tensors", "description": "Trim the feature tensor X and target tensor Y to ensure they are temporally aligned and have the same number of samples.", "dependencies": [3, 4], "details": "Due to the 12-step future prediction in Y, the last 12 samples of X have no corresponding target. <PERSON>m both tensors to the same length by removing the last 12 time steps. The final X should have shape (N_times - 12, 41, 71, 6) and Y should have shape (N_times - 12, 41, 71, 12).", "status": "done", "testStrategy": "Confirm that the first dimension (the time/sample dimension) of the final X and Y tensors are equal. Verify that `len(X) == len(Y)`."}]}, {"id": 4, "title": "Data Standardization", "description": "Standardize the input features `X` using Z-Score normalization. The scaler (mean and std) must be computed only on the training set and then applied to all data splits (train, val, test).", "details": "Implement a `StandardScaler` class or use `sklearn.preprocessing.StandardScaler`. \n1. Reshape `X_train` to `(N_samples, N_features)` where `N_features=6`. \n2. Fit the scaler on this reshaped training data to compute the mean and standard deviation for each of the 6 feature channels. \n3. Save the fitted scaler object (e.g., using `pickle` or `joblib`). \n4. Apply the `transform` method to the training, validation, and test sets. \n5. The scaler will also be needed for inverse transformation of predictions during evaluation.", "testStrategy": "After transformation, verify that the mean of each feature channel in the training set is approximately 0 and the standard deviation is approximately 1. Check that the scaler object is saved and can be loaded successfully.", "priority": "high", "dependencies": [3], "status": "done", "subtasks": [{"id": 1, "title": "Reshape Training Data for Scaler", "description": "Reshape the `X_train` numpy array into a 2D format of `(N_samples, N_features)` where `N_features` is 6. This is a prerequisite for fitting the scikit-learn scaler.", "dependencies": [], "details": "The `StandardScaler` from scikit-learn expects a 2D array as input. The current `X_train` data might have more dimensions (e.g., `(samples, timesteps, features)`). It needs to be reshaped to `(-1, 6)` before it can be used with the scaler's `fit` method.", "status": "done", "testStrategy": "Assert that the shape of the reshaped `X_train` array is `(num_total_samples, 6)` where `num_total_samples` is the product of the original first two dimensions."}, {"id": 2, "title": "Fit StandardScaler on Reshaped Training Data", "description": "Instantiate `sklearn.preprocessing.StandardScaler` and use its `fit` method on the reshaped training data from the previous step. This computes the mean and standard deviation required for normalization.", "dependencies": [1], "details": "Create an instance of `StandardScaler`. Call the `scaler.fit(reshaped_X_train)` method. This step only calculates the scaling parameters (mean and std) from the training data and stores them within the scaler object.", "status": "done", "testStrategy": "After fitting, inspect the scaler object. Verify that `scaler.mean_` and `scaler.scale_` (the standard deviation) are both numpy arrays of length 6."}, {"id": 3, "title": "Apply Transformation to All Data Splits", "description": "Use the `transform` method of the fitted scaler to apply the Z-score normalization to the training, validation, and test sets.", "dependencies": [2], "details": "First, reshape `X_val` and `X_test` to the same 2D format `(-1, 6)`. Then, call `scaler.transform()` on each of the three reshaped data splits (`X_train`, `X_val`, `X_test`) to get their standardized versions.", "status": "done", "testStrategy": "Check the transformed training data. Its mean for each feature should be approximately 0 and its standard deviation should be approximately 1. Verify the shapes of `X_val_scaled` and `X_test_scaled` match their reshaped inputs."}, {"id": 4, "title": "Save the Fitted Scaler Object", "description": "Serialize the fitted `StandardScaler` object and save it to a file using `joblib` or `pickle`. This allows for its reuse in future inference pipelines and for inverse transformations.", "dependencies": [2], "details": "Use a library like `joblib` to persist the scaler object to disk. For example: `joblib.dump(scaler, 'scaler.joblib')`. The saved file is essential for applying the exact same transformation to new, unseen data.", "status": "done", "testStrategy": "Confirm that a file (e.g., `scaler.joblib`) is created in the designated output directory. Attempt to load the file back using `joblib.load()` and verify the loaded object is an instance of `StandardScaler` with the correct `mean_` values."}, {"id": 5, "title": "Implement Scaler Loading for Inverse Transformation", "description": "Create and test a utility function to load the saved scaler from disk. This function will be used during the evaluation phase to inverse-transform model predictions back to their original scale.", "dependencies": [4], "details": "Define a function, e.g., `load_scaler(path)`, that takes a file path and returns the loaded scaler object using `joblib.load()`. This encapsulates the loading logic, making it easy to call before the inverse transformation of model outputs is required.", "status": "done", "testStrategy": "Call the loading function with the path to the saved scaler. Use the loaded scaler to `inverse_transform` a small, known array of scaled data. Verify the output is numerically correct and no longer has a mean of 0 and std dev of 1."}]}, {"id": 5, "title": "Sliding Window Sampler Dataset", "description": "Create a PyTorch Dataset class that implements sliding window sampling to generate input/output pairs for the model.", "details": "Create a `torch.utils.data.Dataset` subclass. The `__init__` method will take the standardized `X`, the target `Y`, and time features (`hour`, `day_of_year`). The `__len__` method should return the total number of possible samples. The `__getitem__` method will slice the data using a sliding window approach: \n- Input `X` sample shape: `(L_in, 41, 71, 6)` where `L_in=336`. \n- Target `Y` sample shape: `(L_out, 41, 71, 1)` where `L_out=12`. \n- It should also return the corresponding time features for the input sequence.", "testStrategy": "Instantiate the dataset and retrieve a few samples. Verify the shapes and data types of `X_sample`, `Y_sample`, and time features. Ensure the window slides correctly by checking that `sample[i+1]` is shifted by one time step from `sample[i]`.", "priority": "high", "dependencies": [4], "status": "done", "subtasks": [{"id": 1, "title": "Define Dataset Class and `__init__` Method", "description": "Create the `SlidingWindowSamplerDataset` class inheriting from `torch.utils.data.Dataset`. Implement the `__init__` method to accept and store the input data arrays (standardized `X`, target `Y`, `hour`, `day_of_year`) and window length parameters (`L_in`, `L_out`).", "dependencies": [], "details": "The `__init__` method should store the provided NumPy arrays or Tensors as instance attributes. It should also concatenate the time features (`hour`, `day_of_year`) into a single `(N, 2)` tensor, `self.time_features`, for easier handling in `__getitem__`.", "status": "done", "testStrategy": "Instantiate the class with mock data and verify that all instance attributes (`self.X`, `self.Y`, `self.time_features`, `self.L_in`, `self.L_out`) are set correctly and have the expected shapes."}, {"id": 2, "title": "Implement `__len__` Method", "description": "Implement the `__len__` special method to calculate and return the total number of possible samples that can be generated by the sliding window.", "dependencies": [1], "details": "The length should be calculated using the formula: `total_timesteps - L_in - L_out + 1`. `total_timesteps` is the length of the first dimension of the input `X` data. This method is required by PyTorch's `Dataset` API for components like `DataLoader`.", "status": "done", "testStrategy": "After instantiating the dataset with mock data of a known length (e.g., 500 timesteps), assert that `len(dataset)` returns the correctly calculated value (e.g., `500 - 336 - 12 + 1 = 153`)."}, {"id": 3, "title": "Implement Slicing Logic in `__getitem__`", "description": "Implement the core data slicing logic within the `__getitem__` method. Given an index, this method will determine the start and end points for the input and target sequences and slice the data arrays.", "dependencies": [1], "details": "For a given `idx`, the input slice for `X` and `time_features` should be `[idx : idx + self.L_in]`. The target slice for `Y` should be `[idx + self.L_in : idx + self.L_in + self.L_out]`. Slice the instance attributes `self.X`, `self.Y`, and `self.time_features` accordingly.", "status": "done", "testStrategy": "This logic will be tested as part of the full `__getitem__` test in subtask 5. Internally, one can add print statements or use a debugger to check if the calculated indices are correct for a given `idx`."}, {"id": 4, "title": "Structure and Return Data from `__getitem__`", "description": "Finalize the `__getitem__` method to return the sliced data as a structured dictionary of PyTorch tensors, ensuring correct data types.", "dependencies": [3], "details": "The method should return a dictionary with keys like `'x'`, `'y'`, and `'x_time_features'`. The values should be the corresponding data slices from subtask 3, converted to `torch.Tensor` with `dtype=torch.float32`. This structured output is beneficial for clarity and ease of use in the training loop.", "status": "done", "testStrategy": "Retrieve an item using `dataset[0]`. Verify that the returned object is a dictionary containing the specified keys. Check that each value is a `torch.Tensor` and has the correct `dtype`."}, {"id": 5, "title": "Create Unit Test for the Dataset", "description": "Develop a comprehensive unit test to verify the correctness of the entire `SlidingWindowSamplerDataset` implementation, focusing on shapes and data continuity.", "dependencies": [2, 4], "details": "The test should instantiate the dataset with small, predictable mock data (e.g., `np.arange` reshaped). It must assert that `len()` returns the correct value. It should retrieve a sample (e.g., `dataset[10]`) and assert that the shapes of the returned tensors (`x`, `y`, `x_time_features`) match the specifications: `(336, 41, 71, 6)`, `(12, 41, 71, 1)`, and `(336, 2)`. Also, verify that the first timestep of the `y` sample logically follows the last timestep of the `x` sample in the original mock data.", "status": "done", "testStrategy": "Execute the test script. The test passes if all assertions succeed, confirming the dataset's length, sample shapes, data types, and slicing logic are correct."}]}, {"id": 6, "title": "Implement SpatioTemporalEmbedding Module", "description": "Implement the `SpatioTemporalEmbedding` module which creates learnable embeddings for nodes, time of day, and day of year.", "details": "Create an `nn.<PERSON><PERSON><PERSON>` named `SpatioTemporalEmbedding`. \n- `self.node_embedding = nn.Embedding(num_embeddings=2911, embedding_dim=D_emb)` \n- `self.tod_embedding = nn.Embedding(num_embeddings=12, embedding_dim=D_emb)` \n- `self.doy_embedding = nn.Embedding(num_embeddings=366, embedding_dim=D_emb)` \nThe `forward` method will take the input data and time features, look up the corresponding embeddings, and add them to the input features. The embedding dimension `D_emb` should be a configurable hyperparameter.", "testStrategy": "Create an instance of the module and pass a dummy input tensor and dummy time indices. Verify the output shape. Check that the embedding weights are registered as trainable parameters.", "priority": "medium", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Define SpatioTemporalEmbedding Class Structure", "description": "Create the basic Python class structure for the SpatioTemporalEmbedding module, inheriting from torch.nn.Module, and define its `__init__` method signature.", "dependencies": [], "details": "Create a new file for the module, e.g., `models/modules.py`. Define the class `SpatioTemporalEmbedding(nn.Module)`. The `__init__` method should be defined to accept `D_emb` (embedding dimension) and `num_nodes` as parameters to ensure configurability.", "status": "done", "testStrategy": "Code review to ensure the class is defined correctly and inherits from `nn.Module`. The `__init__` method should accept the specified parameters."}, {"id": 2, "title": "Initialize Embedding Layers", "description": "In the `__init__` method, initialize the three required `nn.Embedding` layers for nodes, time of day, and day of year.", "dependencies": [1], "details": "Using the `D_emb` parameter from the initializer, create the three embedding layers: `self.node_embedding = nn.Embedding(num_embeddings=2911, embedding_dim=D_emb)`, `self.tod_embedding = nn.Embedding(num_embeddings=12, embedding_dim=D_emb)`, and `self.doy_embedding = nn.Embedding(num_embeddings=366, embedding_dim=D_emb)`. The number of embeddings for TOD and DOY can be hardcoded as they are fixed.", "status": "done", "testStrategy": "Instantiate the module with a specific `D_emb` value. Verify that the three embedding layer attributes exist and that their `embedding_dim` and `num_embeddings` properties match the configured values."}, {"id": 3, "title": "Implement the `forward` Method Logic", "description": "Implement the `forward` method to perform the embedding lookups and combine them.", "dependencies": [2], "details": "The `forward` method should accept an input tensor `x` and a `time_features` tensor. The `time_features` tensor will contain indices for time of day and day of year. The method should look up the corresponding embeddings for each node, time of day, and day of year. After lookup, the three resulting embedding tensors should be added together.", "status": "done", "testStrategy": "Pass dummy tensors with valid indices to the `forward` method. Use a debugger or print statements to inspect the shapes and values of the individual looked-up embedding tensors before they are combined."}, {"id": 4, "title": "Handle Tensor Broadcasting and Final Combination", "description": "Ensure correct tensor shape manipulation and broadcasting within the `forward` method so that the combined spatio-temporal embedding can be added to the input features.", "dependencies": [3], "details": "The node, TOD, and DOY embeddings must be expanded or broadcasted to a compatible shape before summation. The final combined embedding tensor should have a shape that can be added to the input tensor `x`. This typically involves adding dimensions using `unsqueeze` or `expand` to match the batch, node, and time step dimensions of `x`.", "status": "done", "testStrategy": "Create a unit test that passes input tensors `x` and `time_features` with specific batch, node, and time step dimensions. Assert that the shape of the final output tensor (after adding the combined embedding to `x`) is identical to the shape of the input tensor `x`."}, {"id": 5, "title": "Write Unit Test for the Complete Module", "description": "Create a comprehensive unit test to validate the entire `SpatioTemporalEmbedding` module, from instantiation to the `forward` pass.", "dependencies": [4], "details": "In a test file, create a test case that: 1. Initializes the `SpatioTemporalEmbedding` module with a sample `D_emb`. 2. Creates realistic dummy input tensors for `x` and `time_features` with appropriate shapes and `dtype` (long for indices). 3. Executes the `forward` pass. 4. Asserts that the output tensor has the correct shape and `dtype`. 5. Asserts that the output values are not NaN and have been modified from the original input `x`.", "status": "done", "testStrategy": "Run the test suite (e.g., via pytest) and confirm that the test for `SpatioTemporalEmbedding` passes successfully."}]}, {"id": 7, "title": "Implement SpatialEncoder Module (GATv2)", "description": "Implement the `SpatialEncoder` module using Graph Attention Network v2 (GATv2) to capture spatial dependencies at each time step.", "details": "Create an `nn.<PERSON><PERSON><PERSON>` named `SpatialEncoder`. Use `torch_geometric.nn.GATv2Conv`. The module's `forward` method will receive batched node features of shape `(B*L_in, N_nodes, D_in)` and the pre-computed `edge_index`. It will apply the GATv2 layer to process the graph data and return updated node features of shape `(B*L_in, N_nodes, D_out)`. The input features need to be reshaped before passing them to the GNN layer.", "testStrategy": "Pass a dummy batch of node features and the loaded `edge_index` through the module. Check for correct output shape. Ensure that the model can handle batched graph data correctly.", "priority": "medium", "dependencies": [2], "status": "done", "subtasks": [{"id": 1, "title": "Define SpatialEncoder Class and __init__ Method", "description": "Create the basic structure for the `SpatialEncoder` module. This involves defining a new Python class that inherits from `torch.nn.Module` and setting up its `__init__` method to accept necessary configuration.", "dependencies": [], "details": "The class must be named `SpatialEncoder` and inherit from `nn.Module`. The `__init__` method should accept arguments such as `in_channels`, `out_channels`, `heads`, and `dropout` to configure the internal GATv2 layer.", "status": "done", "testStrategy": "Instantiate the `SpatialEncoder` class with sample parameters and verify that no errors are raised and the object is created successfully."}, {"id": 2, "title": "Instantiate GATv2Conv Layer", "description": "Within the `__init__` method of the `SpatialEncoder`, instantiate the `torch_geometric.nn.GATv2Conv` layer and assign it as a class member.", "dependencies": [1], "details": "Create a member variable, e.g., `self.gat_conv`, and assign it an instance of `GATv2Conv`. Use the parameters passed to `__init__` (`in_channels`, `out_channels`, `heads`, `dropout`) to initialize the GATv2 layer. Set `concat=True` as is standard for multi-head attention.", "status": "done", "testStrategy": "After instantiating the `SpatialEncoder`, check that the `gat_conv` attribute exists and is an instance of `torch_geometric.nn.GATv2Conv`."}, {"id": 3, "title": "Implement forward Method and Input Reshaping", "description": "Define the `forward` method for the `SpatialEncoder`. The method must accept the batched node features and edge index, and then reshape the input features to be compatible with the `GATv2Conv` layer.", "dependencies": [1], "details": "The `forward` method signature should be `forward(self, x, edge_index)`. The input `x` has a shape of `(B*L_in, N_nodes, D_in)`. It needs to be reshaped into a 2D tensor of shape `(B*L_in * N_nodes, D_in)` before being passed to the GNN layer. Store the original batch dimensions (`B*L_in`, `N_nodes`) for output reshaping.", "status": "done", "testStrategy": "Create a dummy tensor with the shape `(B*L_in, N_nodes, D_in)` and pass it to a partial `forward` implementation. Assert that the reshaped tensor has the correct 2D shape `(B*L_in * N_nodes, D_in)`."}, {"id": 4, "title": "Apply GATv2 Layer and Reshape Output", "description": "In the `forward` method, after reshaping the input, apply the `GATv2Conv` layer to the data and then reshape the output back to the required batched format.", "dependencies": [2, 3], "details": "Pass the reshaped input tensor and `edge_index` to the `self.gat_conv` layer. The output from the GATv2 layer will have a shape of `(B*L_in * N_nodes, heads * out_channels)`. This output must be reshaped back to `(B*L_in, N_nodes, heads * out_channels)` before being returned.", "status": "done", "testStrategy": "Pass dummy input tensors through the complete `forward` method. Verify that the final output tensor has the expected shape `(B*L_in, N_nodes, D_out)`, where `D_out` is `heads * out_channels`."}, {"id": 5, "title": "Create Unit Test for SpatialEncoder Module", "description": "Develop a unit test to validate the complete functionality, shape consistency, and forward pass execution of the `SpatialEncoder` module.", "dependencies": [4], "details": "The test should instantiate the `SpatialEncoder`, create realistic dummy input tensors for `x` (e.g., shape `(4, 50, 16)`) and `edge_index` (e.g., shape `(2, 200)`), and perform a forward pass. Assert that the output shape is correct and that the forward pass executes without raising any exceptions.", "status": "done", "testStrategy": "Run the unit test using a framework like `pytest`. The test must pass, confirming that the module behaves as specified in the parent task."}]}, {"id": 8, "title": "Implement TemporalEncoder Module (SeisMoLLM Style)", "description": "Implement the `TemporalEncoder` module, including the multi-scale convolutional embedder and latent patching, based on the SeisMoLLM architecture.", "details": "Create an `nn.<PERSON><PERSON><PERSON>` named `TemporalEncoder`. \n1. **MultiScaleConvEmbedder**: Implement a series of 1D convolutional blocks (`Multi_Scale_Conv_Block`) with `conv_strides=[2, 2, 1, 1]`. This will downsample the time series length from 336 to 84. \n2. **LatentPatching**: After the convolutions, reshape the sequence. Use `einops.rearrange` to divide the sequence of length 84 into 21 patches (`patch_size=4`). Each patch will be flattened and projected to match the LLM's hidden dimension (768). The input shape will be `(B*N, 336, D_in)` and output `(B*N, 21, 768)`.", "testStrategy": "Pass a dummy tensor of shape `(B*N, 336, D)` through the module. Verify the output shape is `(B*N, 21, 768)`. Check the downsampling logic and the patching mechanism step-by-step.", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Implement the `Multi_Scale_Conv_Block` Module", "description": "Create a `nn.Mo<PERSON>le` named `Multi_Scale_Conv_Block`. This block will serve as the fundamental building block for the `MultiScaleConvEmbedder`, performing 1D convolution at multiple scales.", "dependencies": [], "details": "The block should accept `in_channels`, `out_channels`, and `stride` as arguments. It should contain multiple parallel `nn.Conv1d` layers with different kernel sizes (e.g., 3, 5, 7), each followed by `BatchNorm1d` and a GELU activation. The outputs of these parallel paths should be concatenated along the channel dimension and then passed through a final `nn.Conv1d` layer (with the specified `stride`) to produce the final output of `out_channels`.", "status": "done", "testStrategy": "Instantiate the block with sample parameters. Create a dummy input tensor of shape `(B, C_in, L_in)` and verify the output tensor shape is `(B, C_out, L_out)`, where `L_out` is correctly calculated based on the input length, padding, and stride."}, {"id": 2, "title": "Assemble the `MultiScaleConvEmbedder` Module", "description": "Create the `MultiScaleConvEmbedder` `nn.Module` by sequentially stacking four instances of the `Multi_Scale_Conv_Block`.", "dependencies": [1], "details": "Use an `nn.ModuleList` or `nn.Sequential` to chain four `Multi_Scale_Conv_Block`s. Configure each block with the specified strides from the list `[2, 2, 1, 1]`. Ensure the input and output channels for each consecutive block are correctly matched. The overall module should downsample the input sequence length by a factor of 4 (from 336 to 84).", "status": "done", "testStrategy": "Create a dummy input tensor of shape `(B, D_in, 336)`. Pass it through the `MultiScaleConvEmbedder` instance and assert that the output tensor's shape is `(B, D_latent, 84)`, where `D_latent` is the output channel dimension of the final block."}, {"id": 3, "title": "Implement the Latent Patching and Projection Layer", "description": "Create the logic for reshaping the latent sequence from the convolutional embedder into patches and projecting them to the LLM's hidden dimension.", "dependencies": [], "details": "This component, which can be a standalone `nn.Module`, will take the output from the `MultiScaleConvEmbedder`. It must first permute the dimensions from `(B, D_latent, 84)` to `(B, 84, D_latent)`. Then, use `einops.rearrange` to reshape the sequence into 21 patches of size 4, following the pattern `b (p l) d -> b p (l d)` where `p=21` and `l=4`. Finally, apply an `nn.Linear` layer to project the flattened patch dimension `(4 * D_latent)` to the target dimension of 768.", "status": "done", "testStrategy": "Create a dummy input tensor of shape `(B, 84, D_latent)`. Pass it through this layer and assert the output shape is `(B, 21, 768)`."}, {"id": 4, "title": "Integrate Components into the `TemporalEncoder` Module", "description": "Combine the `MultiScaleConvEmbedder` and the latent patching/projection logic into the final `nn.<PERSON><PERSON>le` named `TemporalEncoder`.", "dependencies": [2, 3], "details": "The `TemporalEncoder` module's `__init__` method will instantiate the `MultiScaleConvEmbedder` and the projection layer. Its `forward` method will define the complete data flow: 1. Accept an input of shape `(B*N, 336, D_in)`. 2. Permute dimensions to `(B*N, D_in, 336)` for `nn.Conv1d`. 3. Pass through the `MultiScaleConvEmbedder`. 4. Pass the result to the latent patching and projection layer to produce the final output.", "status": "done", "testStrategy": "The primary test for this integration will be the comprehensive unit test in the next subtask. A simple forward pass with a dummy tensor can be used to check for runtime errors during development."}, {"id": 5, "title": "Write Unit Test for the `TemporalEncoder`", "description": "Create a comprehensive unit test to verify the end-to-end functionality and dimensional correctness of the complete `TemporalEncoder` module.", "dependencies": [4], "details": "In a dedicated test file, instantiate the `TemporalEncoder` with appropriate parameters (`D_in`, `D_llm=768`, etc.). Create a random tensor with the exact input shape `(B*N, 336, D_in)`, for example with B=2, N=3, D_in=7. Pass this tensor through the encoder instance. Assert that the output tensor has the exact shape `(B*N, 21, 768)`. Additionally, perform a backward pass to ensure gradients are computed correctly.", "status": "done", "testStrategy": "Execute the unit test using a testing framework like `pytest`. The test should pass, confirming that the input/output shapes are correct and the module is free of runtime errors."}]}, {"id": 9, "title": "Implement LLMBackbone with GPT-2 and LoRA", "description": "Implement the `LLMBackbone` module by loading a pre-trained GPT-2 model and applying LoRA for parameter-efficient fine-tuning.", "details": "Create an `nn.<PERSON><PERSON><PERSON>` named `<PERSON><PERSON><PERSON>bone`. \n1. Use the `transformers` library to load `AutoModel.from_pretrained('gpt2')`. \n2. Truncate the model to use only the first 3 transformer layers. \n3. Define a `peft.LoraConfig` with `r=16`, `lora_alpha=32`, and `target_modules=['c_attn']` (or `q_attn` if needed, `c_attn` is common for GPT2). \n4. Use `peft.get_peft_model` to wrap the GPT-2 model with the LoRA config. \n5. Implement logic to freeze all original GPT-2 parameters except for the LoRA adapters, LayerNorm layers, and positional encodings.", "testStrategy": "Instantiate the module and check which parameters have `requires_grad=True`. The number of trainable parameters should be significantly smaller than the total parameters. Pass a dummy tensor of shape `(B, 21, 768)` and verify the output shape is the same.", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Create LLMBackbone Class and Load GPT-2 Model", "description": "Define the basic structure of the `LLMBackbone` class, inheriting from `torch.nn.Module`. In the constructor, load the pre-trained 'gpt2' model using `transformers.AutoModel.from_pretrained`.", "dependencies": [], "details": "Create a new Python file for the module. The `LLMBackbone` class `__init__` method should initialize the parent `nn.Module` and load 'gpt2', storing it as an instance attribute like `self.model`.", "status": "done", "testStrategy": "Instantiate the `LLMBackbone` class. Verify that `self.model` is an instance of a `transformers` GPT-2 model class and is not None."}, {"id": 2, "title": "Truncate GPT-2 Model to 3 Transformer Layers", "description": "Modify the loaded GPT-2 model to reduce its depth. The model should be truncated to use only its first 3 transformer blocks.", "dependencies": [1], "details": "Within the `LLMBackbone` constructor, after loading the model, access its list of transformer blocks (typically `model.h`). Slice this list to retain only the first 3 elements, effectively removing the deeper layers.", "status": "done", "testStrategy": "After instantiating the class, inspect the `self.model.h` attribute (or the equivalent for the GPT-2 model structure). Assert that its length is exactly 3."}, {"id": 3, "title": "Define LoRA Configuration and Apply to Model", "description": "Define the LoRA configuration using `peft.LoraConfig` and apply it to the truncated GPT-2 model to create a `PeftModel`.", "dependencies": [2], "details": "Create a `peft.LoraConfig` instance with `r=16`, `lora_alpha=32`, and `target_modules=['c_attn']`. Use `peft.get_peft_model` to wrap the truncated model with this configuration. The resulting `PeftModel` should replace the original model attribute.", "status": "done", "testStrategy": "Check that `self.model` is an instance of `peft.PeftModel`. Inspect the submodules of the first transformer block to confirm that the `c_attn` module has been replaced by a `peft.lora.Linear` layer containing `lora_A` and `lora_B` weights."}, {"id": 4, "title": "Implement Selective Parameter Freezing", "description": "Write the logic to freeze the original GPT-2 parameters while keeping the LoRA adapters, LayerNorm layers, and positional encodings trainable.", "dependencies": [3], "details": "Iterate through all named parameters of the `PeftModel`. Set `param.requires_grad = False` by default. Then, iterate again and set `param.requires_grad = True` for any parameter whose name contains 'lora_', 'ln_' (for LayerNorm), or 'wpe' (for positional encodings).", "status": "done", "testStrategy": "Create a helper function or script that lists all trainable parameters (where `requires_grad` is True). Verify that the list only contains parameters from LoRA adapters, LayerNorm layers, and positional encodings, and that all other parameters are frozen."}, {"id": 5, "title": "Implement the Forward Pass", "description": "Implement the `forward` method for the `LLMBackbone` module to process input tensors through the LoRA-adapted model.", "dependencies": [4], "details": "The `forward` method should accept `input_ids` and `attention_mask` tensors. It will call the underlying `self.model` with these inputs and return the `last_hidden_state` from the model's output object.", "status": "done", "testStrategy": "Create dummy `input_ids` and `attention_mask` tensors of a specific batch size and sequence length. Pass them to an instance of `LLMBackbone`. Check that the forward pass executes without errors and that the returned tensor has the expected shape `(batch_size, sequence_length, hidden_size)`."}]}, {"id": 10, "title": "Implement PredictionHead Module", "description": "Implement the `PredictionHead` module, which maps the features from the LLM backbone to the final prediction sequence.", "details": "Create a simple `nn.Module` named `PredictionHead`. It will consist of one or more `nn.Linear` layers. The `forward` method will take the LLM's output of shape `(B*N_nodes, 21, 768)`. It should first flatten the last two dimensions or take the representation of the last token, then apply a linear layer to project the features to the desired output length `L_out=12`. The final output shape should be `(B*N_nodes, 12)`.", "testStrategy": "Pass a dummy tensor of shape `(B*N, 21, 768)` into the head. Verify that the output shape is `(B*N, 12)`.", "priority": "medium", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Define PredictionHead Class Skeleton", "description": "Create the basic file and class structure for the PredictionHead module. This involves creating a new Python file and defining the class that inherits from torch.nn.Module.", "dependencies": [], "details": "In a new file, e.g., `modules/prediction_head.py`, define a class `PredictionHead(nn.Module)`. It should include a basic `__init__` method that calls `super().__init__()` and a placeholder for the `forward` method.", "status": "done", "testStrategy": "Verify that the Python file can be created and the module can be imported into another script without causing a syntax error."}, {"id": 2, "title": "Implement the __init__ Method", "description": "Implement the constructor for the PredictionHead class to initialize the necessary neural network layers.", "dependencies": [1], "details": "In the `__init__` method, define a single `nn.Linear` layer. The input dimension for this layer should be calculated from the input shape, which is `21 * 768` after flattening. The output dimension should be hardcoded to `L_out=12` as specified.", "status": "done", "testStrategy": "Instantiate the `PredictionHead` module and print its structure (`print(model)`). Verify that the `nn.Linear` layer is present and has the correct input and output feature dimensions (in_features=16128, out_features=12)."}, {"id": 3, "title": "Implement the forward Method", "description": "Implement the forward pass logic that processes the input tensor and produces the final output.", "dependencies": [2], "details": "The `forward` method must accept an input tensor `x` of shape `(B*N_nodes, 21, 768)`. Inside the method, first, flatten the last two dimensions of `x` to get a tensor of shape `(B*N_nodes, 21 * 768)`. Then, pass this flattened tensor through the `nn.Linear` layer defined in the `__init__` method.", "status": "done", "testStrategy": "Create a dummy torch tensor with the shape `(10, 21, 768)` (representing B*N_nodes=10). Pass this tensor to an instance of the `PredictionHead` module. Assert that the output tensor's shape is `(10, 12)`."}, {"id": 4, "title": "Refactor for Configurability and Add Docstrings", "description": "Refactor the module to accept configuration parameters instead of hardcoded values and add comprehensive documentation.", "dependencies": [3], "details": "Modify the `__init__` method to accept arguments like `input_dim` and `output_dim`. Use these arguments to define the `nn.Linear` layer. Add clear docstrings to the class, `__init__`, and `forward` methods, explaining their purpose, parameters (including shapes), and return values.", "status": "done", "testStrategy": "Review the code to ensure docstrings are clear and complete. Instantiate the module with different input/output dimensions to confirm the refactoring works correctly."}, {"id": 5, "title": "Create a Unit Test for the Module", "description": "Develop a formal unit test to verify the correctness and stability of the PredictionHead module.", "dependencies": [4], "details": "Using a testing framework like `pytest`, create a test function. In this function, instantiate the `PredictionHead` with specific dimensions (e.g., input_dim=21*768, output_dim=12). Create a random tensor of the expected input shape `(B*N_nodes, 21, 768)`. Pass it through the module and assert that the output tensor has the correct shape `(B*N_nodes, 12)` and `dtype`.", "status": "done", "testStrategy": "Execute the test suite and confirm that the test for the `PredictionHead` module passes successfully. This will serve as a regression test for future changes."}]}, {"id": 11, "title": "Assemble Full TEC-MoLLM Model", "description": "Assemble all implemented modules into the final `TEC-MoLLM` model, defining the complete forward pass from input data to prediction.", "details": "Create the main `TEC-MoLLM` `nn.Module`. Its `__init__` will instantiate `SpatioTemporalEmbedding`, `SpatialEncoder`, `TemporalEncoder`, `LLMBackbone`, and `PredictionHead`. The `forward` method will orchestrate the data flow as per the Mermaid diagram: \n1. Input `(B, 336, N, 6)` -> Embedding \n2. Reshape & pass through `SpatialEncoder` \n3. Reshape & pass through `TemporalEncoder` -> `(B*N, 21, 768)` \n4. Pass through `LLMBackbone` \n5. Pass through `PredictionHead` -> `(B*N, 12)` \n6. Reshape to final output `(B, 12, N, 1)`.", "testStrategy": "Instantiate the full model. Create a dummy input batch `(B, 336, N, 6)`, dummy time features, and the `edge_index`. Perform a forward pass and assert that the final output tensor has the expected shape `(B, 12, N, 1)`. Check for any shape mismatches between modules.", "priority": "high", "dependencies": [6, 7, 8, 9, 10], "status": "done", "subtasks": [{"id": 1, "title": "Define TEC-MoLLM Class and Initialize Sub-modules", "description": "Create the main `TEC-MoLLM` class inheriting from `torch.nn.Module`. In the `__init__` method, instantiate all required sub-modules: `SpatioTemporalEmbedding`, `SpatialEncoder`, `TemporalEncoder`, `LLMBackbone`, and `PredictionHead`.", "dependencies": [], "details": "The `__init__` method should accept configuration parameters for each sub-module and pass them during instantiation. The class should be defined in a new file, e.g., `tec_mollm/model.py`. Ensure all module instances are assigned as class attributes (e.g., `self.spatial_encoder = SpatialEncoder(...)`).", "status": "done", "testStrategy": "Verify that the `TEC-MoLLM` object can be instantiated without errors using mock configurations. Check that all sub-modules (`spatio_temporal_embedding`, `spatial_encoder`, etc.) are present as attributes of the instantiated object."}, {"id": 2, "title": "Implement Initial Data Processing in Forward Pass", "description": "Implement the first part of the `forward` method, processing the input tensor through the embedding and spatial encoding layers as per the data flow diagram.", "dependencies": [1], "details": "The `forward` method will take an input tensor `x` with shape `(<PERSON>, 336, <PERSON>, 6)`. It should first pass `x` through `self.spatio_temporal_embedding`. The output should then be reshaped and passed through `self.spatial_encoder`.", "status": "done", "testStrategy": "Create a dummy input tensor with the expected shape (e.g., B=2, N=10). Pass it through this partial `forward` method and assert that the output shape from the `SpatialEncoder` is correct and that no runtime errors occur."}, {"id": 3, "title": "Implement Temporal Encoding in Forward Pass", "description": "Continue the `forward` method implementation by reshaping the output from the spatial encoder and passing it through the temporal encoder.", "dependencies": [2], "details": "Take the output from the `SpatialEncoder` step. Reshape it to match the `TemporalEncoder`'s expected input format. Pass the reshaped tensor through `self.temporal_encoder`. The expected output shape from this stage is `(B*N, 21, 768)`.", "status": "done", "testStrategy": "Using a mock output from the previous subtask's test, pass it through the temporal encoding step. Verify the output tensor has the exact shape `(B*N, 21, 768)`."}, {"id": 4, "title": "Implement LLM and Prediction Head Processing", "description": "Complete the core prediction logic in the `forward` method by passing the temporal features through the `LLMBackbone` and `PredictionHead`.", "dependencies": [3], "details": "The output from the `TemporalEncoder` `(B*N, 21, 768)` is fed directly into `self.llm_backbone`. The resulting features are then passed to `self.prediction_head` to get the predictions, which should have a shape of `(B*N, 12)`.", "status": "done", "testStrategy": "Use a mock tensor with shape `(B*N, 21, 768)` as input. Pass it through the `LLMBackbone` and `PredictionHead` and assert that the final output shape is `(B*N, 12)`."}, {"id": 5, "title": "Finalize Forward Pass and Add End-to-End Test", "description": "Add the final reshaping step to the `forward` method to produce the model's output in the specified format and create a comprehensive test for the entire model's forward pass.", "dependencies": [4], "details": "Take the output from the `PredictionHead` `(B*N, 12)` and reshape it to the final output format `(B, 12, N, 1)`. Create a unit test that instantiates the full `TEC-MoLLM` model, passes a dummy input tensor `(B, 336, N, 6)`, and verifies that the final output tensor has the correct shape `(B, 12, N, 1)` and data type.", "status": "done", "testStrategy": "Execute a complete end-to-end test. The test should create a dummy input tensor, pass it through the entire `model.forward()` call, and assert the final output shape is `(B, 12, N, 1)`. This validates the integration of all modules and the correctness of the entire data flow."}]}, {"id": 12, "title": "Implement Evaluation Metrics", "description": "Implement the evaluation metrics: MAE, RMSE, R-squared, and Pearson Correlation Coefficient. The function should handle inverse scaling of predictions before calculation.", "details": "Create a Python script `metrics.py`. Implement functions for `mae`, `rmse`, `r2_score`, and `pearson_r`. These functions will take `y_true` and `y_pred` as input. Create a wrapper class or function that first loads the saved `scaler` object (from Task 4) and applies `inverse_transform` to both the predictions and the ground truth before computing the metrics. The final reported metrics should be the average over all 12 prediction horizons.", "testStrategy": "Test each metric function with known inputs and expected outputs. For the wrapper, provide scaled dummy data, inverse transform it, and check if the result is the original unscaled data. Ensure the averaging across horizons is calculated correctly.", "priority": "medium", "dependencies": [4], "status": "done", "subtasks": [{"id": 1, "title": "Implement Core Metric Functions", "description": "Create the `metrics.py` file and implement the individual functions for MAE, RMSE, R-squared, and Pearson Correlation Coefficient using standard libraries.", "dependencies": [], "details": "In a new `src/metrics.py` file, implement four separate functions: `mae(y_true, y_pred)`, `rmse(y_true, y_pred)`, `r2_score(y_true, y_pred)`, and `pearson_r(y_true, y_pred)`. Use `numpy`, `scikit-learn.metrics`, and `scipy.stats` for the calculations. These functions will form the core calculation engine.", "status": "done", "testStrategy": "Create a `tests/test_metrics.py` file. For each function, write a unit test with predefined `y_true` and `y_pred` numpy arrays and assert that the output matches a pre-calculated, known correct value."}, {"id": 2, "title": "Create Scaler Loading and Inverse Transform Utility", "description": "Develop a function that loads the saved scaler object and applies the inverse transform to input data arrays.", "dependencies": [], "details": "In `metrics.py`, create a helper function `inverse_transform_data(y_true_scaled, y_pred_scaled, scaler_path)`. This function will load the scaler object from the specified `scaler_path` using `joblib.load()`. It will then apply the `scaler.inverse_transform()` method to both `y_true_scaled` and `y_pred_scaled` and return the resulting unscaled arrays.", "status": "done", "testStrategy": "Save a sample `MinMaxScaler` object using `joblib.dump()`. Write a test that loads this scaler, provides sample scaled data to `inverse_transform_data`, and asserts that the output matches the original, known unscaled data."}, {"id": 3, "title": "Develop Main Evaluation Wrapper Function", "description": "Create a single wrapper function that orchestrates the inverse scaling and calculation of all four evaluation metrics for a single prediction horizon.", "dependencies": [1, 2], "details": "In `metrics.py`, create a function `calculate_metrics_for_horizon(y_true_scaled, y_pred_scaled, scaler_path)`. This function will first call `inverse_transform_data` to get unscaled data. Then, it will call the four core metric functions (`mae`, `rmse`, etc.) on the unscaled data and return a dictionary containing the results, e.g., `{'mae': value, 'rmse': value, 'r2_score': value, 'pearson_r': value}`.", "status": "done", "testStrategy": "Write an integration test that uses sample scaled data and a saved scaler. Call `calculate_metrics_for_horizon` and verify that the returned dictionary contains the correct keys and that the metric values are within an acceptable tolerance of pre-calculated expected values."}, {"id": 4, "title": "Adapt Evaluation for Multiple Prediction Horizons", "description": "Modify the evaluation logic to handle inputs with 12 prediction horizons, calculating metrics for each horizon individually.", "dependencies": [3], "details": "Create a new top-level function `evaluate_model(y_true_all_horizons, y_pred_all_horizons, scaler_path)`. This function will assume inputs have a shape like `(num_samples, 12)`. It will loop from `i = 0` to `11`, slicing the `i`-th column for both `y_true` and `y_pred`. In each iteration, it will call `calculate_metrics_for_horizon` and store the resulting dictionary of metrics. The function will return a list of 12 such dictionaries.", "status": "done", "testStrategy": "Create test data with a shape of `(10, 12)`. Call `evaluate_model` and assert that the output is a list of length 12, and that each element is a dictionary containing the four metric keys."}, {"id": 5, "title": "Calculate and Report Average Metrics", "description": "Compute the average of each metric across all 12 prediction horizons from the list of results.", "dependencies": [4], "details": "Modify the `evaluate_model` function from the previous subtask. After generating the list of 12 metric dictionaries, add logic to compute the average of each metric type. For example, iterate through the list, sum all 'mae' values, and divide by 12. The function should return a single dictionary containing the final averaged metrics: `{'mae_avg': ..., 'rmse_avg': ..., 'r2_score_avg': ..., 'pearson_r_avg': ...}`.", "status": "done", "testStrategy": "Using the list of dictionaries generated from the test in the previous subtask, manually calculate the average for each metric. Assert that the final output dictionary from the `evaluate_model` function matches these manually calculated average values."}]}, {"id": 13, "title": "Create Training and Validation Loop", "description": "Develop the main training and validation script. This includes setting up the optimizer, loss function, training loop, validation loop, and model checkpointing.", "details": "Create `train.py`. \n1. Initialize `DataLoader`s for train and validation sets using the `Dataset` from Task 5. \n2. Instantiate the `TEC-MoLLM` model (Task 11). \n3. Set up the AdamW optimizer, targeting only the trainable parameters (LoRA, LayerNorms, etc.). \n4. Define the loss function (e.g., `nn.MSELoss` or `nn.L1Loss`). \n5. Implement the training loop: iterate through epochs and batches, perform forward/backward pass, update weights. \n6. Implement the validation loop: run inference on the validation set, compute metrics using the functions from Task 12. \n7. Implement checkpointing to save the model with the best validation score.", "testStrategy": "Run the training script for a few epochs on a small subset of the data. Verify that the loss decreases. Check that model checkpoints are being saved correctly. Ensure metrics are logged for both training and validation sets.", "priority": "high", "dependencies": [5, 11, 12], "status": "done", "subtasks": [{"id": 1, "title": "Initialize Training Script Components", "description": "Create the `train.py` script and set up the initial components. This includes instantiating the DataLoaders for training and validation sets, loading the TEC-MoLLM model, and configuring the AdamW optimizer and the loss function.", "dependencies": [], "details": "In `train.py`, import necessary libraries (torch, etc.). Use the Dataset class from Task 5 to create `DataLoader` instances for both 'train' and 'val' splits. Instantiate the `TEC-MoLLM` model from Task 11. Identify and filter for trainable parameters (LoRA weights, LayerNorms) and initialize the AdamW optimizer to target only these parameters. Define the loss function, such as `nn.MSELoss` or `nn.L1Loss`.", "status": "done", "testStrategy": "Run the script to ensure all objects (DataLoaders, model, optimizer, loss_fn) are instantiated without errors. Print the number of trainable parameters to verify the optimizer is configured correctly."}, {"id": 2, "title": "Implement the Core Training Loop Function", "description": "Develop the function for a single training epoch. This function will iterate through batches from the training DataLoader, execute the model's forward pass, calculate the loss, perform the backward pass for gradient computation, and update the model's weights.", "dependencies": [1], "details": "Create a function, e.g., `train_one_epoch(model, dataloader, optimizer, loss_fn, device)`. Inside the function, set the model to training mode (`model.train()`). Iterate over the training DataLoader, moving each batch to the correct device. For each batch, perform the standard training steps: zero gradients (`optimizer.zero_grad()`), forward pass, loss calculation, backward pass (`loss.backward()`), and optimizer step (`optimizer.step()`).", "status": "done", "testStrategy": "Execute the function for a single epoch using a small subset of the training data. Verify that the model's weights change after one step and that the reported loss is a valid number."}, {"id": 3, "title": "Implement the Validation Loop and Metric Calculation Function", "description": "Create a function to evaluate the model's performance on the validation set. This involves running the model in inference mode, calculating the validation loss, and computing performance metrics using functions from Task 12.", "dependencies": [1], "details": "Create a function, e.g., `validate(model, dataloader, loss_fn, device)`. Set the model to evaluation mode (`model.eval()`) and use a `torch.no_grad()` context manager. Iterate over the validation DataLoader, perform the forward pass to get predictions, and calculate the validation loss. Use the metric calculation functions from Task 12 to compute all required evaluation metrics. Aggregate and return the average validation loss and metrics.", "status": "done", "testStrategy": "Run the validation function on a small subset of the validation data with a pre-trained or randomly initialized model. Ensure that metrics are calculated correctly and that no errors occur. Confirm that model gradients are not updated."}, {"id": 4, "title": "Integrate Training and Validation into a Main Epoch Controller", "description": "Combine the training and validation functions into a main loop that controls the overall training process. This loop will iterate for a specified number of epochs, calling the training function and then the validation function for each epoch, and log the results.", "dependencies": [2, 3], "details": "In the main execution block of `train.py`, create a loop `for epoch in range(num_epochs):`. Inside this loop, call the `train_one_epoch` function to train the model. After training, call the `validate` function to evaluate it. Print or log the results from both functions (e.g., training loss, validation loss, validation metrics) for each epoch to monitor progress.", "status": "done", "testStrategy": "Run the full script for a small number of epochs (e.g., 2-3) on a data subset. Verify that both training and validation execute sequentially for each epoch and that the logs are produced as expected."}, {"id": 5, "title": "Implement Model Checkpointing", "description": "Add functionality to the main training loop to save model checkpoints. The script should track the best validation score achieved so far and save the model's state dictionary whenever a new best score is recorded.", "dependencies": [4], "details": "Initialize a variable to track the best validation metric (e.g., `best_val_loss = float('inf')`). Within the main epoch loop, after the validation step, compare the current validation metric with the best one. If the current metric is better, update the best metric variable and save the model's state dictionary using `torch.save(model.state_dict(), 'best_model.pth')`. Log a message indicating that a new best model has been saved.", "status": "done", "testStrategy": "Run the training script for a few epochs. Check that a model checkpoint file (`.pth`) is created or updated only when the validation metric improves. Attempt to load the saved state dictionary back into a model instance to ensure it was saved correctly."}]}, {"id": 14, "title": "Implement Baseline Models (HA, SARIMA)", "description": "Implement the baseline models specified in the PRD: Historical Average (HA) and SARIMA.", "details": "Create a `baselines.py` script. \n- **HA**: For each grid point and each of the 12 time-of-day slots, calculate the average TEC value from the entire training set. The prediction is this constant average. \n- **SARIMA**: Use `statsmodels.tsa.statespace.sarimax`. Since running SARIMA on 2911 individual time series is computationally expensive, select a few representative grid points to model. The seasonal component `s` would be 12 (for the 2-hour resolution daily cycle).", "testStrategy": "For HA, verify that the predictions are constant and match the pre-calculated averages. For SARIMA, fit the model on a single time series and check if it produces forecasts with the correct shape. Evaluate both models on the validation set using the same metrics script (Task 12).", "priority": "medium", "dependencies": [1], "status": "done", "subtasks": [{"id": 1, "title": "Setup Baseline Script and Data Loading", "description": "Create the `baselines.py` script and implement robust functions to load the preprocessed training data required for both HA and SARIMA models.", "dependencies": [], "details": "The script should be located in an appropriate source directory (e.g., `src/models/`). The data loading function must efficiently read the TEC data, likely from an HDF5 or NetCDF file, into a memory-efficient structure like an xarray Dataset or a NumPy array, ensuring timestamps and grid point dimensions are correctly parsed.", "status": "done", "testStrategy": "Write a unit test to confirm the data loading function returns an array/dataset with the expected dimensions (e.g., time_steps, grid_points). Assert that a few known values from the source file are correctly loaded."}, {"id": 2, "title": "Implement and Train Historical Average (HA) Model", "description": "Implement the Historical Average (HA) model by calculating the average TEC value for each grid point and each of the 12 daily time-of-day slots from the entire training set.", "dependencies": [1], "details": "The implementation should process the loaded training data to compute a mean value for each of the 2911 grid points at each of the 12 time slots (00-02h, 02-04h, etc.). The resulting model, which is essentially a lookup table of averages, should be saved to a file (e.g., a NumPy array of shape (2911, 12)) for efficient prediction later.", "status": "done", "testStrategy": "Verify the output model file exists and has the correct shape (2911, 12). Manually calculate the average for a single grid point and a single time slot on a small data subset and assert that the scripted calculation matches."}, {"id": 3, "title": "Select Representative <PERSON>rid Points for SARIMA", "description": "Develop and apply a documented strategy to select a small, representative subset of the 2911 grid points for the computationally intensive SARIMA modeling.", "dependencies": [1], "details": "Since training SARIMA on all 2911 time series is infeasible, select 3-5 representative points. The selection criteria should be justified and documented. Methods could include: 1) Geographic sampling (e.g., one equatorial, one mid-latitude, one polar). 2) Clustering time series by statistical properties (e.g., variance, autocorrelation) and selecting the series closest to each cluster's centroid. The output should be a configuration or list of the selected grid point indices.", "status": "done", "testStrategy": "Confirm that the selection process outputs a list of valid integer indices within the range [0, 2910]. The documented rationale for the selection should be reviewed for clarity and soundness."}, {"id": 4, "title": "Implement and Fit SARIMA Model for Selected Points", "description": "Using `statsmodels.tsa.statespace.sarimax`, implement and fit a SARIMA model for each of the representative grid points selected in the previous subtask.", "dependencies": [1, 3], "details": "For each selected grid point index, extract its full time series from the training data. Instantiate a SARIMA model with a seasonal period `s=12`. A reasonable starting order could be (p,d,q)=(1,1,1) and (P,D,Q,s)=(1,1,1,12). Fit the model to the data and serialize the fitted model object (e.g., using pickle) for each grid point, saving it to disk for later use in prediction.", "status": "done", "testStrategy": "Verify that the script runs without errors and creates a saved model file for each selected grid point. For one of the models, load it and print the `summary()` to inspect model convergence and coefficient statistics."}, {"id": 5, "title": "Create Unified Prediction and Evaluation Interface", "description": "Develop a unified interface within `baselines.py` to generate predictions from the saved HA and SARIMA models on a test dataset and calculate evaluation metrics.", "dependencies": [2, 4], "details": "Create a function or class that accepts a model identifier ('HA' or 'SARIMA') and a test data array. For 'HA', it should load the average-value table and generate predictions based on the time-of-day slot. For 'SARIMA', it should load the corresponding saved model object and use its `.forecast()` or `.predict()` method. The interface should return predictions in a standard format and compute metrics like MAE and RMSE against the ground truth.", "status": "done", "testStrategy": "Write a test that calls the prediction function for both 'HA' and 'SARIMA' on a small, known test set. Assert that the shape of the returned prediction array is correct. Verify that the calculated MAE/RMSE are positive floating-point numbers."}]}, {"id": 15, "title": "Final Model Evaluation on Test Set", "description": "Create a final testing script to evaluate the best trained TEC-MoLLM and the baseline models on the held-out test set.", "details": "Create `test.py`. \n1. Load the test `DataLoader`. \n2. Load the best saved `TEC-MoLLM` model checkpoint from the training process. \n3. Run the model in `eval()` mode on the test set to generate predictions. \n4. Run the baseline models (Task 14) on the test set to generate their predictions. \n5. Use the metrics module (Task 12) to compute and compare the performance (MAE, RMSE, R^2, Pearson r) of all models. \n6. Print or save the results in a clear, tabular format.", "testStrategy": "Execute the script and ensure it runs without errors. Verify that it loads the correct test data and model checkpoint. Double-check that the final reported metrics are calculated on the inverse-transformed (i.e., real-scale) values. The output should be a clear comparison table.", "priority": "medium", "dependencies": [12, 13, 14], "status": "done", "subtasks": [{"id": 1, "title": "Initialize Test Script and Load Data", "description": "Create the `test.py` file and implement the basic structure for testing. This includes setting up argument parsing for necessary file paths (data, model checkpoints) and initializing the `DataLoader` for the held-out test set.", "dependencies": [], "details": "Create `test.py` with a `main` function. Use the `argparse` library to accept command-line arguments for the test data path, TEC-MoLLM checkpoint path, and an output directory. Instantiate the test `DataLoader` and ensure it loads data correctly.", "status": "done", "testStrategy": "Run the script with dummy arguments. Verify that it executes without errors and successfully loads a batch of test data. Print the shape of a loaded batch to confirm."}, {"id": 2, "title": "Implement TEC-MoLLM Inference", "description": "Add functionality to `test.py` to load the best saved TEC-MoLLM checkpoint. Set the model to evaluation mode (`model.eval()`) and run inference on the entire test set to generate predictions.", "dependencies": [1], "details": "Implement a function that takes the model checkpoint path, loads the model state dictionary, and prepares it for inference. Iterate through the test `DataLoader` within a `torch.no_grad()` context to generate predictions. Store the predictions and corresponding ground truth labels.", "status": "done", "testStrategy": "Load the model and run inference on a single batch of data. Check that the output predictions tensor has the expected dimensions and data type."}, {"id": 3, "title": "Implement Baseline Models Inference", "description": "Integrate the trained baseline models (from Task 14) into the testing script. Load each baseline model and generate its predictions on the same test set for a direct comparison.", "dependencies": [1], "details": "Create a loop or a set of functions to handle the loading and prediction generation for each baseline model (e.g., RandomForest, XGBoost). Ensure predictions are generated for the entire test set and stored alongside the TEC-MoLLM predictions.", "status": "done", "testStrategy": "For each baseline model, load it and generate predictions for a small subset of the test data. Verify the predictions are in the correct format and stored properly."}, {"id": 4, "title": "Compute Performance Metrics", "description": "Utilize the metrics module (from Task 12) to calculate the performance of TEC-MoLLM and all baseline models. Compute MAE, RMSE, R^2, and Pearson r for each set of predictions against the ground truth labels.", "dependencies": [2, 3], "details": "Import the necessary functions from the metrics module. After generating all predictions, call the metric functions for each model's predictions. Store the results in a dictionary where keys are model names and values are dictionaries of their scores.", "status": "done", "testStrategy": "Create a small, fixed set of mock predictions and ground truth labels. Pass them to the metric calculation logic and assert that the computed scores (MAE, RMSE, etc.) match pre-calculated expected values."}, {"id": 5, "title": "Format and Save Results Table", "description": "Aggregate the computed metrics for all models into a clear, tabular format. Print the final comparison table to the console and save it to a file in the specified output directory.", "dependencies": [4], "details": "Use a library like `pandas` or `tabulate` to create the results table. The table should have rows for each model and columns for each metric (MAE, RMSE, R^2, Pearson r). Save the resulting table as a `.csv` or `.md` file.", "status": "done", "testStrategy": "Create a mock dictionary of computed metrics. Pass this dictionary to the table generation function and verify that the output string is correctly formatted and that the file is saved successfully."}]}], "metadata": {"created": "2025-07-01T09:03:33.725Z", "updated": "2025-07-01T13:15:30.800Z", "description": "Tasks for master context"}}}