#!/bin/bash
# TEC-MoLLM 环境配置脚本
# 用于重建与当前环境兼容的Miniconda环境

echo "=== TEC-MoLLM 环境配置脚本 ==="
echo "当前环境信息："
echo "- Python: 3.10.12"
echo "- PyTorch: 2.1.1"
echo "- CUDA: 12.1"
echo "- Ubuntu: 22.04.4 LTS"
echo ""

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "错误: 未找到conda命令，请先安装Miniconda或Anaconda"
    exit 1
fi

# 检查并安装mamba
if ! command -v mamba &> /dev/null; then
    echo "安装mamba以提高包管理速度..."
    conda install -n base -c conda-forge mamba -y
    echo "mamba安装完成！"
else
    echo "mamba已安装，使用mamba进行包管理"
fi

# 使用mamba或conda
PACKAGE_MANAGER="mamba"
if ! command -v mamba &> /dev/null; then
    PACKAGE_MANAGER="conda"
    echo "使用conda作为包管理器"
else
    echo "使用mamba作为包管理器（更快）"
fi

# 创建新环境
ENV_NAME="tec-mollm"
echo "创建新的环境: $ENV_NAME"
$PACKAGE_MANAGER create -n $ENV_NAME python=3.10 -y

# 激活环境
echo "激活环境..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate $ENV_NAME

# 添加清华镜像源
echo "配置镜像源..."
$PACKAGE_MANAGER config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/rapidsai/
$PACKAGE_MANAGER config --add channels conda-forge
$PACKAGE_MANAGER config --add channels defaults

# 安装基础科学计算包
echo "安装基础科学计算包..."
$PACKAGE_MANAGER install -y numpy=1.24.3 scipy=1.10.1 matplotlib=3.8.4

# 安装PyTorch (CUDA 12.1版本)
echo "安装PyTorch..."
pip install torch==2.1.1 torchvision==0.16.1 torchaudio==2.1.1 --index-url https://download.pytorch.org/whl/cu121

# 安装PyTorch Geometric
echo "安装PyTorch Geometric..."
pip install torch-geometric==2.4.0
pip install torch-scatter==2.1.2 torch-sparse==0.6.18 -f https://data.pyg.org/whl/torch-2.1.0+cu121.html

# 安装Transformers和PEFT
echo "安装Transformers和相关库..."
pip install transformers>=4.30.0 peft>=0.4.0 accelerate

# 安装其他核心依赖
echo "安装其他核心依赖..."
pip install -r requirements.txt

echo ""
echo "=== 环境配置完成 ==="
echo "激活环境: conda activate $ENV_NAME"
echo "验证安装: python -c \"import torch; print(f'PyTorch: {torch.__version__}, CUDA: {torch.version.cuda}')\""
echo ""
echo "💡 提示："
echo "- mamba已安装，后续可以使用 'mamba install' 代替 'conda install' 获得更快的安装速度"
echo "- mamba命令与conda完全兼容，但速度更快"
echo ""
